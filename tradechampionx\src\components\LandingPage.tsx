import React from 'react';
import { motion } from 'framer-motion';
import {
  Trophy,
  Users,
  Zap,
  Shield,
  TrendingUp,
  Gamepad2,
  ArrowR<PERSON>,
  Star,
  Crown,
  Target
} from 'lucide-react';
import FloatingElements from './FloatingElements';
import AnimatedCounter from './AnimatedCounter';
import InteractiveButton from './InteractiveButton';

const LandingPage: React.FC = () => {
  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900">
      {/* Hero Section */}
      <section className="relative min-h-screen flex items-center justify-center overflow-hidden">
        {/* Animated Background Elements */}
        <div className="absolute inset-0">
          <div className="absolute top-20 left-20 w-72 h-72 bg-primary-500/20 rounded-full blur-3xl animate-pulse-slow"></div>
          <div className="absolute bottom-20 right-20 w-96 h-96 bg-accent-purple/20 rounded-full blur-3xl animate-pulse-slow delay-1000"></div>
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[600px] h-[600px] bg-accent-pink/10 rounded-full blur-3xl animate-float"></div>
          <FloatingElements />
        </div>

        {/* Navigation */}
        <nav className="absolute top-0 left-0 right-0 z-50 p-6">
          <div className="max-w-7xl mx-auto flex items-center justify-between">
            <motion.div 
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6 }}
              className="flex items-center space-x-2"
            >
              <div className="w-10 h-10 bg-gradient-primary rounded-lg flex items-center justify-center">
                <Crown className="w-6 h-6 text-white" />
              </div>
              <span className="text-2xl font-bold gradient-text">TradeChampionX</span>
            </motion.div>
            
            <motion.div 
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="hidden md:flex items-center space-x-8"
            >
              <a href="#features" className="text-white/80 hover:text-white transition-colors">Features</a>
              <a href="#how-it-works" className="text-white/80 hover:text-white transition-colors">How It Works</a>
              <a href="#community" className="text-white/80 hover:text-white transition-colors">Community</a>
              <button className="glass px-6 py-2 rounded-full text-white hover:bg-white/20 transition-all duration-300">
                Connect Wallet
              </button>
            </motion.div>
          </div>
        </nav>

        {/* Hero Content */}
        <div className="relative z-10 max-w-6xl mx-auto px-6 text-center">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.3 }}
            className="mb-6"
          >
            <div className="inline-flex items-center glass px-4 py-2 rounded-full mb-8">
              <Star className="w-4 h-4 text-yellow-400 mr-2" />
              <span className="text-sm text-white/90">First 100 get OG Badge 🚀</span>
            </div>
          </motion.div>

          <motion.h1
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.4 }}
            className="text-5xl md:text-7xl font-bold mb-6 leading-tight"
          >
            <span className="text-white">The Trading</span>
            <br />
            <span className="gradient-text">Arena Revolution</span>
          </motion.h1>

          <motion.p
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.5 }}
            className="text-xl md:text-2xl text-white/80 mb-12 max-w-3xl mx-auto leading-relaxed"
          >
            Where crypto meets esports. Where traders become legends. 
            Compete in influencer-hosted arenas for real USDC prizes.
          </motion.p>

          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.6 }}
            className="flex flex-col sm:flex-row items-center justify-center gap-4 mb-16"
          >
            <InteractiveButton variant="primary" size="lg">
              Join the Waitlist
              <ArrowRight className="w-5 h-5 ml-2" />
            </InteractiveButton>
            <InteractiveButton variant="secondary" size="lg">
              <Gamepad2 className="w-5 h-5 mr-2" />
              Watch Demo
            </InteractiveButton>
          </motion.div>

          {/* Stats */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.7 }}
            className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-4xl mx-auto"
          >
            <div className="glass-dark p-6 rounded-2xl">
              <div className="text-3xl font-bold gradient-text mb-2">
                <AnimatedCounter end={2.5} prefix="$" suffix="M+" duration={2.5} />
              </div>
              <div className="text-white/70">Total Prize Pool</div>
            </div>
            <div className="glass-dark p-6 rounded-2xl">
              <div className="text-3xl font-bold gradient-text mb-2">
                <AnimatedCounter end={10} suffix="K+" duration={2} />
              </div>
              <div className="text-white/70">Traders Waiting</div>
            </div>
            <div className="glass-dark p-6 rounded-2xl">
              <div className="text-3xl font-bold gradient-text mb-2">
                <AnimatedCounter end={50} suffix="+" duration={1.5} />
              </div>
              <div className="text-white/70">Influencer Partners</div>
            </div>
          </motion.div>
        </div>

        {/* Scroll Indicator */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 1, delay: 1 }}
          className="absolute bottom-8 left-1/2 transform -translate-x-1/2"
        >
          <div className="w-6 h-10 border-2 border-white/30 rounded-full flex justify-center">
            <div className="w-1 h-3 bg-white/50 rounded-full mt-2 animate-bounce"></div>
          </div>
        </motion.div>
      </section>

      {/* Features Section */}
      <section id="features" className="py-24 relative">
        <div className="max-w-7xl mx-auto px-6">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="text-center mb-16"
          >
            <h2 className="text-4xl md:text-5xl font-bold mb-6">
              <span className="gradient-text">Built Different</span>
            </h2>
            <p className="text-xl text-white/70 max-w-3xl mx-auto">
              The first crypto-native trading esports arena where skill meets spectacle, 
              and every trade is a performance.
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {[
              {
                icon: Trophy,
                title: "Influencer Arenas",
                description: "Compete in custom-branded arenas hosted by your favorite crypto influencers with real USDC prizes."
              },
              {
                icon: Shield,
                title: "On-Chain Transparency",
                description: "Smart escrow contracts ensure fair play. See exactly where your money goes with full blockchain transparency."
              },
              {
                icon: Zap,
                title: "Real-Time Trading",
                description: "Lightning-fast execution with professional-grade market data. No slippage, no delays, just pure skill."
              },
              {
                icon: Users,
                title: "Community First",
                description: "Trade with your tribe, not alone. Chat, cheer, and celebrate victories together in real-time."
              },
              {
                icon: TrendingUp,
                title: "Live Leaderboards",
                description: "Watch positions change as trades execute. Build your reputation and climb the ranks."
              },
              {
                icon: Target,
                title: "Instant Payouts",
                description: "Winners get paid in USDC within 24 hours. No waiting, no excuses, just instant gratification."
              }
            ].map((feature, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="glass-dark p-8 rounded-2xl hover:bg-white/10 transition-all duration-300 group"
              >
                <div className="w-12 h-12 bg-gradient-primary rounded-lg flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
                  <feature.icon className="w-6 h-6 text-white" />
                </div>
                <h3 className="text-xl font-semibold text-white mb-4">{feature.title}</h3>
                <p className="text-white/70 leading-relaxed">{feature.description}</p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* How It Works Section */}
      <section id="how-it-works" className="py-24 relative">
        <div className="max-w-7xl mx-auto px-6">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="text-center mb-16"
          >
            <h2 className="text-4xl md:text-5xl font-bold mb-6">
              <span className="gradient-text">From Spectator to Legend</span>
            </h2>
            <p className="text-xl text-white/70 max-w-3xl mx-auto">
              Your journey to trading glory starts here. Four simple steps to enter the arena.
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {[
              {
                step: "01",
                title: "Discovery",
                description: "Browse live arenas and watch leaderboards. No wallet needed to feel the FOMO.",
                icon: "👀"
              },
              {
                step: "02",
                title: "Connection",
                description: "One-click wallet auth. Choose your arena and pay the entry fee.",
                icon: "🔗"
              },
              {
                step: "03",
                title: "Competition",
                description: "Trade with $10k virtual capital. Climb the leaderboard and build your legend.",
                icon: "📈"
              },
              {
                step: "04",
                title: "Victory",
                description: "Win prizes, get paid in USDC, and flex your success to the world.",
                icon: "💎"
              }
            ].map((step, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.2 }}
                viewport={{ once: true }}
                className="relative"
              >
                <div className="glass-dark p-8 rounded-2xl text-center hover:bg-white/10 transition-all duration-300 group">
                  <div className="text-4xl mb-4">{step.icon}</div>
                  <div className="text-sm font-mono text-primary-400 mb-2">{step.step}</div>
                  <h3 className="text-xl font-semibold text-white mb-4">{step.title}</h3>
                  <p className="text-white/70 leading-relaxed">{step.description}</p>
                </div>
                {index < 3 && (
                  <div className="hidden lg:block absolute top-1/2 -right-4 transform -translate-y-1/2">
                    <ArrowRight className="w-6 h-6 text-primary-400" />
                  </div>
                )}
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Social Proof Section */}
      <section className="py-24 relative">
        <div className="max-w-7xl mx-auto px-6">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="text-center mb-16"
          >
            <h2 className="text-4xl md:text-5xl font-bold mb-6">
              <span className="gradient-text">Join the Arena Tribe</span>
            </h2>
            <p className="text-xl text-white/70 max-w-3xl mx-auto">
              Be one of the first 100 to claim an OG Badge and get early access to the very first Arena.
            </p>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            viewport={{ once: true }}
            className="max-w-2xl mx-auto"
          >
            <div className="glass-dark p-8 rounded-2xl text-center">
              <div className="flex items-center justify-center mb-6">
                <div className="flex -space-x-2">
                  {[1, 2, 3, 4, 5].map((i) => (
                    <div key={i} className="w-12 h-12 bg-gradient-primary rounded-full border-2 border-slate-800 flex items-center justify-center">
                      <span className="text-white font-semibold">{i}</span>
                    </div>
                  ))}
                </div>
                <div className="ml-4 text-left">
                  <div className="text-2xl font-bold gradient-text">
                    <AnimatedCounter end={2847} duration={3} />
                  </div>
                  <div className="text-white/70 text-sm">Traders on waitlist</div>
                </div>
              </div>

              <div className="mb-8">
                <div className="bg-slate-800 rounded-full h-2 mb-2">
                  <div className="bg-gradient-primary h-2 rounded-full" style={{ width: '28%' }}></div>
                </div>
                <div className="text-sm text-white/70">28 of 100 OG Badges claimed</div>
              </div>

              <div className="space-y-4">
                <input
                  type="email"
                  placeholder="Enter your email for early access"
                  className="w-full px-6 py-4 bg-slate-800/50 border border-white/20 rounded-full text-white placeholder-white/50 focus:outline-none focus:border-primary-400 transition-colors"
                />
                <InteractiveButton variant="primary" size="lg" className="w-full">
                  Claim Your OG Badge 🚀
                </InteractiveButton>
              </div>

              <p className="text-xs text-white/50 mt-4">
                Join the tribe before we open gates to everyone
              </p>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Footer */}
      <footer className="py-16 border-t border-white/10">
        <div className="max-w-7xl mx-auto px-6">
          <div className="flex flex-col md:flex-row items-center justify-between">
            <div className="flex items-center space-x-2 mb-4 md:mb-0">
              <div className="w-10 h-10 bg-gradient-primary rounded-lg flex items-center justify-center">
                <Crown className="w-6 h-6 text-white" />
              </div>
              <span className="text-2xl font-bold gradient-text">TradeChampionX</span>
            </div>

            <div className="flex items-center space-x-6 text-white/70">
              <a href="#" className="hover:text-white transition-colors">Discord</a>
              <a href="#" className="hover:text-white transition-colors">Twitter</a>
              <a href="#" className="hover:text-white transition-colors">Telegram</a>
              <a href="#" className="hover:text-white transition-colors">Docs</a>
            </div>
          </div>

          <div className="mt-8 pt-8 border-t border-white/10 text-center text-white/50">
            <p>&copy; 2024 TradeChampionX. The future of trading is here. It's beautiful, it's fair, and it's built for the crypto generation.</p>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default LandingPage;
