import React from 'react';
import { motion } from 'framer-motion';

const FloatingElements: React.FC = () => {
  return (
    <div className="absolute inset-0 overflow-hidden pointer-events-none">
      {/* Floating Orbs */}
      <motion.div
        className="absolute w-4 h-4 bg-primary-400 bg-opacity-30 rounded-full blur-sm"
        animate={{
          x: [0, 100, 0],
          y: [0, -50, 0],
        }}
        transition={{
          duration: 8,
          repeat: Infinity,
          ease: "easeInOut"
        }}
        style={{ top: '20%', left: '10%' }}
      />
      
      <motion.div
        className="absolute w-6 h-6 bg-accent-purple bg-opacity-20 rounded-full blur-sm"
        animate={{
          x: [0, -80, 0],
          y: [0, 60, 0],
        }}
        transition={{
          duration: 10,
          repeat: Infinity,
          ease: "easeInOut",
          delay: 2
        }}
        style={{ top: '60%', right: '15%' }}
      />
      
      <motion.div
        className="absolute w-3 h-3 bg-accent-pink bg-opacity-40 rounded-full blur-sm"
        animate={{
          x: [0, 60, 0],
          y: [0, -80, 0],
        }}
        transition={{
          duration: 12,
          repeat: Infinity,
          ease: "easeInOut",
          delay: 4
        }}
        style={{ top: '40%', left: '80%' }}
      />
      
      <motion.div
        className="absolute w-5 h-5 bg-accent-orange bg-opacity-25 rounded-full blur-sm"
        animate={{
          x: [0, -40, 0],
          y: [0, 70, 0],
        }}
        transition={{
          duration: 9,
          repeat: Infinity,
          ease: "easeInOut",
          delay: 1
        }}
        style={{ top: '80%', left: '20%' }}
      />

      {/* Floating Geometric Shapes */}
      <motion.div
        className="absolute w-8 h-8 border border-primary-400 border-opacity-20 rotate-45"
        animate={{
          rotate: [45, 225, 45],
          scale: [1, 1.2, 1],
        }}
        transition={{
          duration: 15,
          repeat: Infinity,
          ease: "linear"
        }}
        style={{ top: '30%', right: '25%' }}
      />
      
      <motion.div
        className="absolute w-6 h-6 border border-accent-purple border-opacity-30 rounded-full"
        animate={{
          scale: [1, 1.5, 1],
          opacity: [0.3, 0.6, 0.3],
        }}
        transition={{
          duration: 6,
          repeat: Infinity,
          ease: "easeInOut",
          delay: 3
        }}
        style={{ top: '70%', right: '40%' }}
      />

      {/* Grid Pattern */}
      <div className="absolute inset-0 opacity-5">
        <div 
          className="w-full h-full"
          style={{
            backgroundImage: `
              linear-gradient(rgba(14, 165, 233, 0.1) 1px, transparent 1px),
              linear-gradient(90deg, rgba(14, 165, 233, 0.1) 1px, transparent 1px)
            `,
            backgroundSize: '50px 50px'
          }}
        />
      </div>
    </div>
  );
};

export default FloatingElements;
