import React from 'react';
import { motion } from 'framer-motion';
import { Play, Trophy } from 'lucide-react';
import FloatingElements from './FloatingElements';
import InteractiveButton from './InteractiveButton';

const HeroSection: React.FC = () => {
  return (
    <section className="relative min-h-screen flex items-center justify-center overflow-hidden">
      {/* Animated Background Elements */}
      <div className="absolute inset-0">
        <div className="absolute top-20 left-20 w-72 h-72 rounded-full blur-3xl pulse-slow"
             style={{backgroundColor: 'rgba(14, 165, 233, 0.2)'}}></div>
        <div className="absolute bottom-20 right-20 w-96 h-96 rounded-full blur-3xl pulse-slow"
             style={{backgroundColor: 'rgba(139, 92, 246, 0.2)', animationDelay: '1s'}}></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[600px] h-[600px] rounded-full blur-3xl animate-float"
             style={{backgroundColor: 'rgba(236, 72, 153, 0.1)'}}></div>
        <FloatingElements />
      </div>

      {/* Hero Content */}
      <div className="relative z-10 max-w-6xl mx-auto px-6 text-center">
        {/* Main Headline */}
        <motion.h1
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.2 }}
          className="text-5xl md:text-7xl lg:text-8xl font-bold mb-8 leading-tight"
        >
          <span className="text-white">Trade in the Arena.</span>
          <br />
          <span className="text-white">Win Like a </span>
          <span className="gradient-text">Champion</span>
          <span className="text-white">.</span>
          <span className="ml-4 text-6xl md:text-8xl">🏆</span>
        </motion.h1>

        {/* Subline */}
        <motion.p
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.4 }}
          className="text-xl md:text-2xl lg:text-3xl text-white text-opacity-90 mb-12 max-w-4xl mx-auto leading-relaxed font-medium"
        >
          The first crypto-native trading esports arena.
          <br className="hidden md:block" />
          <span className="gradient-text font-semibold"> Compete, climb leaderboards, and earn real USDC prizes.</span>
        </motion.p>

        {/* CTA Buttons */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.6 }}
          className="flex flex-col sm:flex-row items-center justify-center gap-6 mb-12"
        >
          {/* Primary CTA - Claim Your OG Badge */}
          <InteractiveButton
            variant="primary"
            size="lg"
            className="text-xl px-16 py-8 font-bold shadow-2xl neon-glow transform hover:scale-105 transition-all duration-300"
          >
            🚀 Claim Your OG Badge
          </InteractiveButton>

          {/* Secondary CTA - Watch Demo */}
          <InteractiveButton 
            variant="secondary" 
            size="lg"
            className="text-xl px-12 py-8 font-semibold border-2 border-white border-opacity-30 hover:border-opacity-60"
          >
            <Play className="w-6 h-6 mr-3 fill-current" />
            Watch Demo
          </InteractiveButton>
        </motion.div>

        {/* Progress Indicator */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.8 }}
          className="max-w-md mx-auto"
        >
          <div className="glass-dark p-6 rounded-2xl">
            {/* Progress Bar */}
            <div className="mb-4">
              <div className="bg-slate-800 bg-opacity-60 rounded-full h-3 mb-3 overflow-hidden">
                <motion.div 
                  className="bg-gradient-primary h-3 rounded-full relative"
                  initial={{ width: 0 }}
                  animate={{ width: '28%' }}
                  transition={{ duration: 2, delay: 1, ease: "easeOut" }}
                >
                  {/* Shimmer effect on progress bar */}
                  <motion.div
                    className="absolute inset-0 bg-gradient-to-r from-transparent via-white via-white to-transparent opacity-30"
                    animate={{ x: ['-100%', '100%'] }}
                    transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
                  />
                </motion.div>
              </div>
              
              {/* Progress Text */}
              <div className="flex items-center justify-between text-sm">
                <span className="text-white text-opacity-70">OG Badges Claimed</span>
                <span className="text-primary-400 font-semibold">28 / 100</span>
              </div>
            </div>

            {/* Micro-text */}
            <p className="text-white text-opacity-80 text-lg font-medium">
              Be one of the first 100 to join. 
              <span className="text-primary-400 font-semibold"> 28 already claimed.</span>
            </p>
          </div>
        </motion.div>

        {/* Floating Trophy Icons */}
        <div className="absolute inset-0 pointer-events-none">
          <motion.div
            className="absolute top-1/4 left-1/4"
            animate={{ 
              y: [0, -20, 0],
              rotate: [0, 5, 0]
            }}
            transition={{ 
              duration: 4, 
              repeat: Infinity, 
              ease: "easeInOut",
              delay: 0.5
            }}
          >
            <Trophy className="w-8 h-8 text-yellow-400 opacity-20" />
          </motion.div>
          
          <motion.div
            className="absolute top-1/3 right-1/4"
            animate={{ 
              y: [0, -15, 0],
              rotate: [0, -5, 0]
            }}
            transition={{ 
              duration: 5, 
              repeat: Infinity, 
              ease: "easeInOut",
              delay: 1.5
            }}
          >
            <Trophy className="w-6 h-6 text-primary-400 opacity-15" />
          </motion.div>
        </div>
      </div>

      {/* Scroll Indicator */}
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 1, delay: 1.5 }}
        className="absolute bottom-8 left-1/2 transform -translate-x-1/2"
      >
        <div className="w-6 h-10 border-2 border-white border-opacity-30 rounded-full flex justify-center">
          <div className="w-1 h-3 bg-white bg-opacity-50 rounded-full mt-2 animate-bounce"></div>
        </div>
      </motion.div>
    </section>
  );
};

export default HeroSection;
