## Product Plan – Crypto Trading Challenges (MVP)

### Vision
- A crypto-native competition platform: influencers host trading challenges; traders join via entry fee; paper trading with live data; winners get on-chain prize payouts.
- **Influencer-branded arenas:** each challenge feels like the host’s personal battleground with custom banner, description, and message to entrants.

### Non-Negotiable MVP Loop
- View challenges (no login required) → connect wallet → pay entry fee → paper trade (market/limit) → live leaderboard → admin-triggered payout from escrow.
- **Spectator funnel:** even non-entrants can follow leaderboards and pool growth to drive FOMO.

## Scope (Trimmed MVP)

### Roles
- Trader: joins challenge, trades, shows on leaderboard.
- Host: application + approval; creates and manages challenges.
- Admin: approves hosts; monitors challenges; triggers payouts.
- **Spectator: no wallet required, can view challenges, pool breakdown, and live leaderboard.**

### Core Features
- Auth: wallet-only (RainbowKit + Wagmi). SIWE for session. View-only mode for non-connected users.
- Chain/Token: Base L2; USDC only.
- Escrow: minimal contract that holds deposits per challenge, shows pool balance, admin sets winners and triggers payout. No infinite approvals.
- Challenges: public only; simple fields; presets for prize splits.  
  - **Host branding (bannerUrl, message, socials) for viral feel.**
- Join Flow: approve+deposit USDC to escrow; backend verifies and admits participant; virtual starting balance (e.g., 10k).
- Trading: paper engine (spot only). Orders: Market + Limit. Server-authoritative fills using licensed data. Deterministic PnL.
- Market Data: licensed provider (Tardis.dev/CoinAPI). Do not redistribute raw Binance commercially.
- Leaderboard: live ROI/equity ranking, public visibility.  
  - **Trader identity lite: wallet alias, cumulative ROI, Top-3 finisher badge.**
- Admin Panel: approve hosts, view challenges/pools, finalize & payout, disputes.

## Prize Pool Logic (Finalized for MVP)

- Platform fee: fixed 10% from gross pool, deducted first.
- Host rake: 0% / 10% / 20% of net pool (post-platform fee); taken before winner distribution.
- Presets (host picks one):
  - HostTakesAll: 100% of net to host (ignore rake).
  - WinnerTakesAll: 100% of winner pool to 1st.
  - Top3: 70/20/10.
  - Top5: 40/25/15/10/10.
- Fewer finishers than preset: truncate and renormalize weights to 100% over actual top-K.
- Rounding: use bps; floor; allocate remainder by rank order (1→N).
- Transparency: display gross, platform fee, net, host rake, winner pool, and per-rank amounts on challenge page; link on-chain tx hashes.  
  - **Add pool visualization UI (bar + breakdown + Etherscan link) as core trust hook.**
- Roadmap: custom host rake % (0–50%) in v2 for influencer flexibility (MVP remains 0/10/20). Until v2, backend override allowed for trusted hosts (whitelist), capped at 50%.

## Policy & Trust Rules (MVP)

- Min entrants: default 5 required to start. Hosts may set higher floors up to 50. If not reached by start time → challenge auto-cancels and all deposits are refunded from escrow.
- Refund policy:
  - Refunds only if: (a) min entrants not met, or (b) admin cancels before start.
  - No refunds after a user joins a running or completed challenge (no rage-quit refunds).
  - Refund gas: platform covers gas at MVP; v2 adds batch refunds to reduce costs.
- Dispute window & SLA:
  - Dispute window: 24 hours after challenge end.
  - Admin review SLA: resolve within 48 hours. During review, challenge shows `status=under_review`.
  - Admin decision is final; results immutable after window closes.
- Payout trust:
  - MVP: admin-triggered payout with on-chain events and visible tx hashes.
  - ≤ 60 days post-beta: migrate to 2-of-3 multisig for admin functions (treasury + ops + cold).
  - Scaling payouts: add payout automation + audit logs post-beta.
- Duration bounds: enforce 1–14 days per challenge.
- Anti-sybil (MVP): 1 wallet = 1 entry per challenge; basic device/browser fingerprint warning and soft-block (backlog for v2 hardening).
- View-only: full browsing and leaderboard visibility without wallet connection.
- Large payout flag: flag host payouts > $10k for manual compliance review (KYC/KYB if required).

## UX/Pages

- Landing: hero, trending challenges, “Apply as Host”.
- Challenge Detail: header (name, host badge, pool, time left, rules link); left: TradingView chart + order ticket; right: live leaderboard; bottom: positions + history; “Join” CTA.  
  - **Host banner + message displayed prominently to entrants and spectators.**
- Pool Visualization: “Total pool $X | Platform fee $Y | Host rake $Z | Winner pool $W” + escrow address/Etherscan link.
- Host Dashboard: create & manage challenges; pool view; status (draft/published/ended/cancelled/under_review).
- Admin: host approvals, challenge monitor, finalize + payout, disputes.
- Rules Page: ranking formula, timing, fees, data source, dispute policy; “skill-based contest” copy.
- Profile (lite): host/trader wallet alias, past challenges, entrants, payouts.  
  - **Trader stats: cumulative ROI, top placements, simple badge counts.**
- Archive: finished challenges remain public; frozen leaderboard; downloadable CSV logs.
- **Spectator Mode: non-wallet users can browse, view leaderboards, and follow pool growth live.**

## Architecture

- Frontend: Next.js App Router (TS), Tailwind + shadcn/ui, TradingView Charting Library.
- State: React Query for server cache; Zustand for terminal state.
- Backend: Next.js API routes (lightweight). SSE/WebSocket for live updates.
- DB: Postgres (Prisma). Redis for leaderboards + pub/sub cache.
- Observability: Sentry; structured logs. Minimal at MVP.
- Security: server-authoritative fills, no client PnL; NTP time sync; single wallet per challenge.

## Data Model (Essentials)

- Users: id, walletAddress(unique), displayName, role, verifiedHost(bool), flags.  
  - **Add: cumulativeROI, top3Finishes (int), badges(json).**
- HostApplications: id, wallet, socials, notes, status(pending|approved|rejected).
- Challenges: id, hostWallet, name, description, bannerUrl, hostMessage, startAt, endAt, allowedSymbols[], startingBalance, entryFeeUSDC, platformFeeBps(=1000), hostRakeBps(0|1000|2000), prizePreset(HOST_TAKES_ALL|WINNER_TAKES_ALL|TOP3|TOP5), escrowAddress, visibility(public), status(draft|live|ended|finalized|cancelled|under_review), minEntrants(default=5, max=50), maxEntrants(optional), disputeWindowEndsAt, finalizedAt, cancelledAt.
- Participants: id, challengeId, wallet, joinedAt, entryTxHash, isEligible, startEquity, refundTxHash(nullable), deviceFingerprintHash(optional).
- Orders: id, participantId, type(market|limit), side(buy|sell), symbol, qty, limitPrice?, status(open|filled|cancelled), createdAt.
- Fills: id, orderId, price, qty, fee, timestamp, tickId.
- Positions: participantId+symbol, qty, avgPrice, unrealizedPnl.
- EquitySnapshots: id, participantId, timestamp, equity.
- Disputes: id, participantId, claim, evidenceUrl, status(open|resolved|rejected), createdAt, resolvedAt, resolutionNotes.
- AuditLogs: id, challengeId, type(event), payloadHash, createdAt (for CSV export + integrity).

## Flows

### Host Application
- Submit wallet + socials → admin review → set `verifiedHost=true`.

### Challenge Creation (Host)
- Fill form → create (status=draft); constraints enforced (1–14 days, USDC entry fee) → publish (status=live) → escrow mapping visible on detail page.  
  - **Includes bannerUrl + hostMessage fields.**

### Pre-Start Checks
- At start time: if `entrants < minEntrants` → status=cancelled; auto-refund all deposits from escrow; show refund tx hashes.

### Join (Trader)
- Connect wallet → approve+deposit USDC to escrow → backend verifies tx → create participant → credit virtual balance.

### Trading
- Client submits order → backend validates challenge window, balance, symbol → fill at next tick best bid/ask → update positions/equity → broadcast updates.

### Leaderboard
- Rank by ROI = (Equity / StartingBalance) − 1; update in near real-time; cache + periodic snapshots.  
  - **Leaderboard shows trader badges & cumulative ROI stats.**

### End, Dispute, Finalize & Payout
- End at `endAt`; freeze new orders; compute standings.
- Dispute window (24h): accept submissions; admin reviews within 48h; display `under_review` status.
- Finalize: set winners; admin triggers on-chain payout per preset; store tx hashes; status=finalized.
- Archive: keep read-only page with frozen leaderboard + CSV export.  
  - **Archive also shows host branding and trader badges for profile flex.**

### Spectator Flow
- No wallet → can view host branding, pool visualization, and live leaderboard.

## Fairness & Anti-Cheat (MVP)
- Server-only fills; reject orders after endAt; fill at next tick (no backfill).
- Single wallet per challenge; soft device fingerprint check.
- Event logs with timestamps; exportable CSV for audits.
- Display data source and fill policy on Rules page.

## Compliance & Policy
- “Skill-based contest” framing; published rules and scoring.
- Geofence high-risk regions (basic IP filter).
- No KYC for traders; flag host payouts > $10k for review; KYB for high-earning hosts considered later.

## Risks & Mitigations
- Wallet-only UX: allow full view-only access; clear join flow guidance.
- Chain fees: start on Base; keep chain-agnostic adapters.
- Admin payouts trust: on-chain escrow, clear payout policy, visible tx hashes; move to multisig ≤ 60 days; automate payouts post-beta.
- Data licensing: use Tardis/CoinAPI commercial plan; attribute data.
- Security: exact-amount allowances; no infinite approvals; reentrancy guards in contract.
- Influencer economics: backend override for rake in MVP (trusted hosts); roadmap custom rake UI (≤50% cap).

## Metrics (MVP)
- Entrants/challenge, completion rate, payout time, dispute rate, DAU.
- Trust: % challenges with escrow, time to payout, refund execution rate.
- Growth: host applications/week, challenge shares.
- Quality: min-entrants pass rate, disputes resolved within SLA (≤48h).

## Delivery Plan

### Milestones
- Week 1: Wallet login (SIWE), DB schema, host application + admin, escrow contract draft (Base testnet).
- Week 2: Challenge create/publish, min-entrants + refund logic, join flow with on-chain verification, TradingView chart with live prices.
- Week 3: Paper engine (market/limit), positions/PnL, equity snapshots, leaderboard.
- Week 4: Dispute window flow (`under_review`), finalize & payout, Rules page, Sentry, private beta (1–2 hosts).
- Weeks 5–6: Stability, Top-3 preset UX polish + pool visualization, archive pages, host profile lite, multisig plan + automation design.

### Acceptance Criteria
- Escrow shows pool; payouts on-chain; challenge page links tx hashes.
- Orders after endAt rejected; fills at next tick; reproducible PnL from logs.
- Join-to-first-trade < 60s; perceived leaderboard latency < 1s; responsive UI.
- Min entrants auto-refund end-to-end with visible tx hashes.
- Dispute window enforced; decisions logged and visible; SLA ≤ 48h.
- Archive + CSV export for finalized challenges.

## Prioritized Backlog

### Launch Blockers (must ship for MVP beta)
- Wallet auth (SIWE) + view-only mode
- Escrow contract (deposit, cancel+refund, payout events), Base + USDC
- Min-entrants auto-cancel + refund (platform covers gas)
- Challenge creation/publish with duration bounds (1–14 days)
- Join flow with on-chain verification
- Paper engine (market/limit) + server-authoritative fills
- Live leaderboard (ROI) + equity snapshots
- Rules page (trust/compliance copy)
- Admin finalize + payout (single challenge)
- Pool visualization + escrow Etherscan link

### Post-Beta (near term)
- Multisig (2-of-3) admin functions
- Payout automation + audit logs (multiple challenges)
- Archive pages + CSV export
- Host profile (lite)
- Backend override for custom rake (trusted hosts)
- Basic device fingerprint soft-block

### Later (v2)
- Custom host rake UI (0–50%)
- Batch refunds on-chain
- Stronger anti-sybil heuristics
- Private challenges, sponsor pools
- Advanced analytics and replays

## Decision Log
- Auth: wallet-only with view-only mode.
- Chain: Base first; architecture chain-agnostic.
- Token: USDC only at launch.
- Prize Presets: HostTakesAll, WinnerTakesAll, Top3, Top5.
- Host Rake: 0/10/20% of net after 10% platform fee; backend override (trusted) until v2; cap 50%.
- Orders: Market + Limit only for MVP.
- Public challenges only.
- Min entrants: default 5; host can set up to 50; auto-refund if not met.
- Refunds: only if min not met or admin cancels pre-start; no mid-challenge refunds; platform covers gas at MVP.
- Duration bounds: 1–14 days.
- Dispute: 24h window; 48h SLA; `under_review` status; admin final decision.
- Payout trust: migrate to 2-of-3 multisig within 60 days; automate post-beta.
- Large payout flag: host payouts > $10k require manual review.
